import os
import logging
import math
from decimal import Decimal
from typing import Dict, List, Optional

import pandas as pd
import pandas_ta as ta  # noqa: F401
from hummingbot.connector.connector_base import ConnectorBase
from hummingbot.core.clock import Clock
from pydantic import Field, field_validator

from hummingbot.client.config.config_data_types import ClientFieldData
from hummingbot.core.data_type.common import OrderType, PositionMode, PriceType, TradeType
from hummingbot.data_feed.candles_feed.candles_factory import CandlesConfig
from hummingbot.strategy.strategy_v2_base import StrategyV2Base, StrategyV2ConfigBase
from hummingbot.strategy_v2.executors.position_executor.data_types import PositionExecutorConfig, TripleBarrierConfig
from hummingbot.strategy_v2.models.executor_actions import CreateExecutorAction, StopExecutorAction


class SimpleDirectionalAOConfig(StrategyV2ConfigBase):
    script_file_name: str = os.path.basename(__file__)
    markets: Dict[str, List[str]] = {}
    candles_config: List[CandlesConfig] = []
    controllers_config: List[str] = []
    exchange: str = Field(default="hyperliquid_perpetual")
    trading_pair: str = Field(default="ETH-USD")
    candles_exchange: str = Field(default="hyperliquid_perpetual")
    candles_pair: str = Field(default="ETH-USD")
    candles_interval: str = Field(default="5m")
    candles_length: int = Field(default=60, gt=0)
    ao_fast_length: int = Field(default=5, gt=0)
    ao_slow_length: int = Field(default=34, gt=0)
    order_amount: Decimal = Field(default=Decimal("0.98"), gt=0)  # Relative amount as percentage
    leverage: int = Field(default=5, gt=0)
    position_mode: PositionMode = Field(default="ONEWAY")
    cooldown: int = Field(default=60, gt=0, client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Cooldown in seconds"
    ))

    # Triple Barrier Configuration
    stop_loss: Decimal = Field(default=Decimal("0.5"), gt=0)
    take_profit: Decimal = Field(default=Decimal("3.0"), gt=0)
    time_limit: int = Field(default=60 * 50000, gt=0)

    # Robustness Configuration
    max_order_retries: int = Field(default=3, gt=0)
    min_order_amount: Decimal = Field(default=Decimal("0.001"), gt=0)
    balance_buffer: Decimal = Field(default=Decimal("0.05"), ge=0, le=1)  # 5% buffer

    @property
    def triple_barrier_config(self) -> TripleBarrierConfig:
        return TripleBarrierConfig(
            stop_loss=self.stop_loss,
            take_profit=self.take_profit,
            time_limit=self.time_limit,
            open_order_type=OrderType.MARKET,
            take_profit_order_type=OrderType.MARKET,
            stop_loss_order_type=OrderType.MARKET,
            time_limit_order_type=OrderType.MARKET,
        )

    @field_validator('position_mode', mode="before")
    @classmethod
    def validate_position_mode(cls, v: str) -> PositionMode:
        if v.upper() in PositionMode.__members__:
            return PositionMode[v.upper()]
        raise ValueError(f"Invalid position mode: {v}. Valid options are: {', '.join(PositionMode.__members__)}")


class SimpleDirectionalAO(StrategyV2Base):
    """
    This strategy uses AO (Awesome Oscillator) to generate trading signals and execute trades based on the AO values.
    It defines the specific parameters and configurations for the AO strategy.
    Enhanced with robustness features for better order execution and error handling.
    """

    account_config_set = False
    cooldown = 0
    _order_failures = 0
    _last_balance_check = 0
    _websocket_logger_suppressed = False

    @classmethod
    def init_markets(cls, config: SimpleDirectionalAOConfig):
        cls.markets = {config.exchange: {config.trading_pair}}

    def __init__(self, connectors: Dict[str, ConnectorBase], config: SimpleDirectionalAOConfig):
        self._last_timestamp = None
        if len(config.candles_config) == 0:
            config.candles_config.append(CandlesConfig(
                connector=config.exchange,
                trading_pair=config.candles_pair,
                interval=config.candles_interval,
                max_records=config.candles_length + 10
            ))
        super().__init__(connectors, config)
        self.config = config
        self.current_ao = None
        self.current_signal = None
        self._suppress_websocket_logs()

    def _suppress_websocket_logs(self):
        """Suppress websocket subscription messages after connection issues"""
        if not self._websocket_logger_suppressed:
            # Suppress various websocket-related loggers
            loggers_to_suppress = [
                "hummingbot.data_feed.candles_feed.candles_base",
                "hummingbot.core.data_type.order_book_tracker_data_source",
                "hummingbot.core.data_type.user_stream_tracker_data_source",
                "hummingbot.connector.derivative.hyperliquid_perpetual.hyperliquid_perpetual_api_order_book_data_source",
                "hummingbot.connector.derivative.hyperliquid_perpetual.hyperliquid_perpetual_user_stream_data_source"
            ]

            for logger_name in loggers_to_suppress:
                logger = logging.getLogger(logger_name)
                # Set to ERROR level to suppress INFO and WARNING messages about subscriptions
                logger.setLevel(logging.ERROR)

            self._websocket_logger_suppressed = True
            # self.notify_hb_app_with_timestamp("Websocket subscription messages suppressed for cleaner output")

    def start(self, clock: Clock, timestamp: float) -> None:
        """
        Start the strategy.
        :param clock: Clock to use.
        :param timestamp: Current time.
        """
        self._last_timestamp = timestamp
        self.apply_initial_setting()

    def create_actions_proposal(self) -> List[CreateExecutorAction]:
        create_actions = []
        if self.cooldown > 0:
            self.cooldown = self.cooldown - 5
            self.notify_hb_app_with_timestamp(f"Cooling down... {self.cooldown}s remaining")
            return create_actions

        # Validate market readiness
        if not self._validate_market_conditions():
            return create_actions

        signal = self.get_signal()
        active_longs, active_shorts = self.get_active_executors_by_side(self.config.exchange,
                                                                        self.config.trading_pair)
        if signal is not None:
            mid_price = self.market_data_provider.get_price_by_type(self.config.exchange,
                                                                    self.config.trading_pair,
                                                                    PriceType.MidPrice)

            # Validate price is reasonable
            if not self._validate_price(mid_price):
                self.notify_hb_app_with_timestamp("Price validation failed, skipping order creation")
                return create_actions

            if signal == 1 and len(active_longs) == 0:
                amount = self.get_amount(TradeType.BUY, mid_price)
                if amount <= 0:
                    return []

                # Additional validation before creating order
                if self._validate_order_creation(TradeType.BUY, amount, mid_price):
                    self.cooldown = self.config.cooldown
                    self._reset_failure_counter()  # Reset on successful validation
                    create_actions.append(CreateExecutorAction(
                        executor_config=PositionExecutorConfig(
                            timestamp=self.current_timestamp,
                            connector_name=self.config.exchange,
                            trading_pair=self.config.trading_pair,
                            side=TradeType.BUY,
                            entry_price=mid_price,
                            amount=amount,
                            triple_barrier_config=self.config.triple_barrier_config,
                            leverage=self.config.leverage
                        )))
                else:
                    self._handle_order_failure("BUY order validation failed")
            elif signal == -1 and len(active_shorts) == 0:
                amount = self.get_amount(TradeType.SELL, mid_price)
                if amount <= 0:
                    return []

                # Additional validation before creating order
                if self._validate_order_creation(TradeType.SELL, amount, mid_price):
                    self.cooldown = self.config.cooldown
                    self._reset_failure_counter()  # Reset on successful validation
                    create_actions.append(CreateExecutorAction(
                        executor_config=PositionExecutorConfig(
                            timestamp=self.current_timestamp,
                            connector_name=self.config.exchange,
                            trading_pair=self.config.trading_pair,
                            side=TradeType.SELL,
                            entry_price=mid_price,
                            amount=amount,
                            triple_barrier_config=self.config.triple_barrier_config,
                            leverage=self.config.leverage
                        )))
                else:
                    self._handle_order_failure("SELL order validation failed")
        return create_actions

    def get_amount(self, position, mid_price):
        """Calculate order amount with enhanced validation and safety checks"""
        try:
            usdt_balance = self.connectors[self.config.exchange].get_available_balance("USD")

            if usdt_balance <= 0:
                self.notify_hb_app_with_timestamp("Insufficient USD balance for trading")
                return Decimal("0")

            # Apply balance buffer to prevent using all available balance
            available_balance = usdt_balance * (Decimal("1") - self.config.balance_buffer)
            usdt = available_balance * self.config.order_amount

            if usdt <= 0:
                self.notify_hb_app_with_timestamp("Calculated USD amount is too small")
                return Decimal("0")

            amount = usdt / mid_price * self.config.leverage

            # Validate minimum order amount
            if amount < self.config.min_order_amount:
                self.notify_hb_app_with_timestamp(f"Order amount {amount} below minimum {self.config.min_order_amount}")
                return Decimal("0")

            # Get trading rules and validate
            trading_rules = self.connectors[self.config.exchange].trading_rules.get(self.config.trading_pair)
            if trading_rules:
                if amount < trading_rules.min_order_size:
                    self.notify_hb_app_with_timestamp(f"Order amount {amount} below exchange minimum {trading_rules.min_order_size}")
                    return Decimal("0")

                # Round to proper precision
                amount = trading_rules.round_down_to_base_precision(amount)

            self.notify_hb_app_with_timestamp(f"""
                    Creating new trade:
                    Position: {position.name}
                    Amount: {amount}
                    Amount USD: {usdt}
                    Available Balance: {usdt_balance}
                    Used Balance: {usdt}""")

            return amount

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error calculating order amount: {e}")
            return Decimal("0")

    def stop_actions_proposal(self) -> List[StopExecutorAction]:
        stop_actions = []
        signal = self.get_signal()
        active_longs, active_shorts = self.get_active_executors_by_side(self.config.exchange,
                                                                        self.config.trading_pair)
        if signal is not None:
            if signal == -1 and len(active_longs) > 0:
                stop_actions.extend([StopExecutorAction(executor_id=e.id) for e in active_longs])
            elif signal == 1 and len(active_shorts) > 0:
                stop_actions.extend([StopExecutorAction(executor_id=e.id) for e in active_shorts])
        return stop_actions

    def get_active_executors_by_side(self, connector_name: str, trading_pair: str):
        active_executors_by_connector_pair = self.filter_executors(
            executors=self.get_all_executors(),
            filter_func=lambda e: e.connector_name == connector_name and e.trading_pair == trading_pair and e.is_active
        )
        active_longs = [e for e in active_executors_by_connector_pair if e.side == TradeType.BUY]
        active_shorts = [e for e in active_executors_by_connector_pair if e.side == TradeType.SELL]
        return active_longs, active_shorts

    def get_signal(self) -> Optional[float]:
        indicator_value = self.get_indicator_value()
        self.current_ao = indicator_value

        if indicator_value > 0:
            signal_value = 1
        elif indicator_value < 0:
            signal_value = -1
        else:
            signal_value = 0
        return signal_value

    def get_indicator_value(self) -> float:
        """Get AO indicator value with enhanced error handling"""
        try:
            candles = self.market_data_provider.get_candles_df(self.config.exchange,
                                                               self.config.candles_pair,
                                                               self.config.candles_interval,
                                                               self.config.candles_length + 10)

            if candles is None or candles.empty:
                self.notify_hb_app_with_timestamp("No candle data available for indicator calculation")
                return 0

            if len(candles) < max(self.config.ao_fast_length, self.config.ao_slow_length):
                self.notify_hb_app_with_timestamp(f"Insufficient candle data: {len(candles)} candles, need at least {max(self.config.ao_fast_length, self.config.ao_slow_length)}")
                return 0

            candles.ta.ao(fast=self.config.ao_fast_length, slow=self.config.ao_slow_length, append=True)
            ao_column = f"AO_{self.config.ao_fast_length}_{self.config.ao_slow_length}"

            if ao_column in candles.columns:
                last_candle = candles.iloc[-1]
                ao = last_candle[ao_column]

                # Validate AO value is not NaN or infinite
                if pd.isna(ao) or not pd.isfinite(ao):
                    self.notify_hb_app_with_timestamp("Invalid AO indicator value (NaN or infinite)")
                    return 0

                return float(ao)
            else:
                self.notify_hb_app_with_timestamp(f"AO column {ao_column} not found in candles data")
                return 0

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error calculating AO indicator: {e}")
            return 0

    def apply_initial_setting(self):
        if not self.account_config_set:
            for connector_name, connector in self.connectors.items():
                if self.is_perpetual(connector_name):
                    connector.set_position_mode(self.config.position_mode)
                    for trading_pair in self.market_data_provider.get_trading_pairs(connector_name):
                        connector.set_leverage(trading_pair, self.config.leverage)
            self.account_config_set = True

    def _validate_market_conditions(self) -> bool:
        """Validate that market conditions are suitable for trading"""
        try:
            # Check if market data provider is ready
            if not self.market_data_provider.ready:
                self.notify_hb_app_with_timestamp("Market data provider not ready")
                return False

            # Check if connector is ready
            connector = self.connectors.get(self.config.exchange)
            if not connector or not connector.ready:
                self.notify_hb_app_with_timestamp("Exchange connector not ready")
                return False

            # Check if we have recent price data
            try:
                mid_price = self.market_data_provider.get_price_by_type(
                    self.config.exchange, self.config.trading_pair, PriceType.MidPrice
                )
                if mid_price <= 0:
                    self.notify_hb_app_with_timestamp("Invalid mid price received")
                    return False
            except Exception as e:
                self.notify_hb_app_with_timestamp(f"Error getting price data: {e}")
                return False

            return True

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error validating market conditions: {e}")
            return False

    def _validate_price(self, price: Decimal) -> bool:
        """Validate that the price is reasonable"""
        try:
            if price <= 0:
                return False

            # Check if price is within reasonable bounds (not too extreme)
            if price > Decimal("1000000") or price < Decimal("0.000001"):
                return False

            return True

        except Exception:
            return False

    def _validate_order_creation(self, side: TradeType, amount: Decimal, price: Decimal) -> bool:
        """Validate order creation parameters"""
        try:
            # Check minimum amount
            if amount < self.config.min_order_amount:
                self.notify_hb_app_with_timestamp(f"Order amount {amount} below minimum {self.config.min_order_amount}")
                return False

            # Check if we have sufficient balance
            connector = self.connectors[self.config.exchange]
            if side == TradeType.BUY:
                required_balance = amount * price / self.config.leverage
                available_balance = connector.get_available_balance("USD")
                if available_balance < required_balance:
                    self.notify_hb_app_with_timestamp(f"Insufficient balance: need {required_balance}, have {available_balance}")
                    return False

            # Check trading rules
            trading_rules = connector.trading_rules.get(self.config.trading_pair)
            if trading_rules:
                if amount < trading_rules.min_order_size:
                    self.notify_hb_app_with_timestamp(f"Order amount {amount} below exchange minimum {trading_rules.min_order_size}")
                    return False
                if amount > trading_rules.max_order_size:
                    self.notify_hb_app_with_timestamp(f"Order amount {amount} above exchange maximum {trading_rules.max_order_size}")
                    return False

            return True

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error validating order creation: {e}")
            return False

    def format_status(self) -> str:
        if not self.ready_to_trade:
            return "Market connectors are not ready."

        original_info = super().format_status()
        extra_info = []

        # Get AO indicator with error handling
        ao_indicator = self.get_indicator_value()
        signal = self.get_signal()

        extra_info.append(f"AO Indicator: {ao_indicator:.6f}")
        extra_info.append(f"Current Signal: {signal}")

        # Add robustness status information
        extra_info.append(f"Order Failures: {self._order_failures}")
        extra_info.append(f"Cooldown: {self.cooldown}s")

        # Market condition status
        market_ready = self._validate_market_conditions()
        extra_info.append(f"Market Conditions: {'✓ Ready' if market_ready else '✗ Not Ready'}")

        # Balance information
        try:
            connector = self.connectors[self.config.exchange]
            balance = connector.get_available_balance("USD")
            extra_info.append(f"Available Balance: {balance:.2f} USD")
        except Exception:
            extra_info.append("Available Balance: Error retrieving")

        # Active positions
        active_longs, active_shorts = self.get_active_executors_by_side(self.config.exchange, self.config.trading_pair)
        extra_info.append(f"Active Positions: {len(active_longs)} Long, {len(active_shorts)} Short")

        # Combine original and extra information
        format_status = f"{original_info}\n\n" + "\n".join(extra_info)
        return format_status

    def on_tick(self):
        """Enhanced on_tick with additional robustness checks"""
        try:
            # Reset order failure counter periodically
            if self.current_timestamp - self._last_balance_check > 300:  # Every 5 minutes
                self._order_failures = max(0, self._order_failures - 1)
                self._last_balance_check = self.current_timestamp

            # Call parent on_tick
            super().on_tick()

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error in on_tick: {e}")

    def _handle_order_failure(self, reason: str):
        """Handle order failures with exponential backoff"""
        self._order_failures += 1
        self.notify_hb_app_with_timestamp(f"Order failure #{self._order_failures}: {reason}")

        # Implement exponential backoff for cooldown
        if self._order_failures > self.config.max_order_retries:
            extended_cooldown = self.config.cooldown * (2 ** min(self._order_failures - self.config.max_order_retries, 5))
            self.cooldown = max(self.cooldown, extended_cooldown)
            self.notify_hb_app_with_timestamp(f"Extended cooldown applied: {self.cooldown}s due to repeated failures")

    def _reset_failure_counter(self):
        """Reset failure counter after successful operations"""
        if self._order_failures > 0:
            self.notify_hb_app_with_timestamp("Resetting failure counter after successful operation")
            self._order_failures = 0